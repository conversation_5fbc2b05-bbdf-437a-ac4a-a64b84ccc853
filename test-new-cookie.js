const http = require('http');

// Test the new cookie value from GraphQL playground
const newCookieValue = 'access_token=3x5%2FN0eVt6W5Jxd4dPgQCeVqiYHEo3WBRlA%2B%2FsFmbNmRPgipBeoQo6F8mLkzDhU%2FSUs%2BVO1ty1LDLAlNDRHMAJE9kK1QOioGSAi%2B%2F6K1BltHNjSGM8rib5QFk7wonWGVRSq3UzE88TEUOyVoVN4ebK9zfvDcBNruj4qeziFfr1L0RYrq76scETmdrxRk6%2Fyg7QgjGfW4fHxcyuPAaoRkExjpFN6CWMF498LsOIGGn%2FYTfngVPKwZJYBtXrnh8nesOn0nC8jRnNuKe95tXO3wIjQ80KQBEPX%2BTRjj4qDP%2BLn3Lfwp8Q0cwOazrtOMLLxbPJYti6bW0ZOcljTu4aH0s450nGZPOAE3TrxhfFh3ZlS5KNe2%2B8nwRLM6j9boU9Hi8FSjkhGZlAKfsakfIYnfsr4TSG3xgMLSssHGXSvhsdsukyXRgjhBodMw2C2%2Fn4MHoGptJc6L%2BI7UvS%2FdLuWm61CaC6DNLemzuS2K%2Fd4TcGj1yFUg3vUzuSCOM8c%2F%2BxSE30%2Fgowq%2BtTH%2Bktjm3ZINiIZsEXrgL%2BXmfUDXwddw7BfvsrFH5HOOSra%2F2e%2FUPiKBivqOhSBIxBZ%2FS6zTWsxLqKkMLBJVMoydAJRUoZAHA6ZBJQABs9I9H88h5q%2FwY3nDzBmE7WwCIRcPHt70hhpr8bX1SMpoQBMJ%2BBOfkXVSio%2BjfjQPb7%2FIyOxuvoEFyMgr5gEX6BJRYdrqf84bs7uDnvh%2Bb8rDlyUC3qqEVyHlOkVNs9zpSl1SWKbIpaYuPK352Xz0OoZD%2FVcGC9AqwwP2%2FmC6o6atub%2Fy7WRIIOowkoTzcRmslTac1%2FCxy9casZbOVRwQys2T%2F4RIaImsPK5wuJ6Kyz7RXhr8Nd1KyNrPDvegFitvMcpIzqgwkwySafWVRm3GadU8uI6uwJAyiiu43%2BNE9w8L8ZweeSjJy2oC%2F94oJkFFbXeyE6y8l5NLjraKXzNpky1N7Y88gTH9--Xjk8tJZ9ZLnyzsS4--3dFsz8YkF%2F2xIT%2B0B8CMHA%3D%3D';

function testNewCookie() {
  console.log('🔍 Testing new cookie value...');
  console.log('🍪 Cookie length:', newCookieValue.length);

  // Test debug endpoint first
  const debugOptions = {
    hostname: 'localhost',
    port: 3060,
    path: '/auth/debug/cookies',
    method: 'GET',
    headers: {
      'Cookie': newCookieValue,
      'Content-Type': 'application/json'
    }
  };

  const debugReq = http.request(debugOptions, (res) => {
    console.log('📊 Debug Status Code:', res.statusCode);

    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });

    res.on('end', () => {
      console.log('📄 Debug Response:');
      try {
        const parsed = JSON.parse(data);
        console.log(JSON.stringify(parsed, null, 2));
        
        // Check if cookie was parsed
        if (parsed.parsedCookies && parsed.parsedCookies.access_token) {
          console.log('✅ Cookie parsed successfully');
          console.log('🔑 Access token length:', parsed.parsedCookies.access_token.length);

          // Now test GraphQL
          testGraphQLWithNewCookie();
        } else {
          console.log('❌ Cookie not parsed correctly');
        }
      } catch (e) {
        console.log('Raw response:', data);
      }
    });
  });

  debugReq.on('error', (e) => {
    console.error('❌ Debug request error:', e.message);
  });

  debugReq.end();
}

function testGraphQLWithNewCookie() {
  console.log('\n🔍 Testing GraphQL with new cookie...');

  const graphqlQuery = {
    query: `query MeCookies {
      meCookies {
        id
        email
        username
        firstName
        lastName
        role
        permissions
        createdAt
        updatedAt
      }
    }`
  };

  const options = {
    hostname: 'localhost',
    port: 3060,
    path: '/graphql',
    method: 'POST',
    headers: {
      'Cookie': newCookieValue,
      'Content-Type': 'application/json'
    }
  };

  const req = http.request(options, (res) => {
    console.log('📊 GraphQL Status Code:', res.statusCode);

    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });

    res.on('end', () => {
      console.log('📄 GraphQL Response:');
      try {
        const parsed = JSON.parse(data);
        console.log(JSON.stringify(parsed, null, 2));
        
        if (parsed.errors) {
          console.log('\n❌ GraphQL Errors detected:');
          parsed.errors.forEach((error, index) => {
            console.log(`Error ${index + 1}: ${error.message}`);
          });
        }
        
        if (parsed.data && parsed.data.meCookies) {
          console.log('\n✅ GraphQL authentication successful!');
        }
      } catch (e) {
        console.log('Raw response:', data);
      }
    });
  });

  req.on('error', (e) => {
    console.error('❌ GraphQL Request error:', e.message);
  });

  req.write(JSON.stringify(graphqlQuery));
  req.end();
}

// Run test
testNewCookie();
