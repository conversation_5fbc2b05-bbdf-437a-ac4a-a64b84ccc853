const fetch = require('node-fetch');

async function testGraphQLAuth() {
  const graphqlUrl = 'http://localhost:3060/graphql';
  
  // Your cookie value
  const cookieValue = 'access_token=mq8i7AFWdKXnt3JgeLuuUzNtbe0vhDHBiBBTzuVJSaqFhW7javeOryA%2FaY7Oz6q5fAZW3OT6BN9L8Zc%2BfE3TkM8KY3E%2BavkeBovvVQsk4RfUnoNahrhk4DH97d3%2BBZQ3CCE08JGBYYDmVJJDTuBwOhwCB3q95Zx%2BIOtL8%2F6JheteiPjL5m3UGsZ7%2Bp1wHU64DJq%2F3walyd5tTvteDJcMBGKs2h1CNkNvOLJQHjGz7fJFDELepIzGoCdVoOt9Yzeq8z6%2FznW%2BulgJtCq%2F9yD7QoOY%2FLawtk8QlqtwAoKolSSmnnWgspwQTbeDUByMY5xaJxeI9gS3Us63S7OkM1DuYAHwJgYX4AOzldrxHYbInkfv2U5bqC%2BW%2B6dnzKrHdS%2Bc4WG5d%2BN5D7HPIKd%2BAYhqOkmQoC9X2p5keQogK5WtJcvlZ%2B4kLGJYsCoWacTGvWjVfZNcE0Y0xQRA3AVf7DhSRGy9MjjQlwK9Dg0XCFvPUfaAH2dbUs0PkFDO8WlKQytXlx4lrnewR5hCxFfDy028ic85Cf7jUJGoU%2FMjjRz6HLw%2BjhJhHtsyS2ERmCVj3wjmld8gLovF4O%2FAaEak1ZZGRfMCXK8eF0xeQ5yekC4h1mmgUVuXWpMAWsW9aC%2BF%2FUOZJhpx4urS5cxJr%2BYLvJt8oGaewgmXQ1ppaaDjPfS0wzWzy6RbGgwBB6u7DCGnTRpsithdWgeyQIWISY9cfsVO3rCBM3vM3F%2F8e18A7bgbq%2BS07sIBnUy3MW1%2FoxhUic%2BgH5Y9F6CxsFXUkYkzLuObDuxjfWMWpHWfJqb%2BVQaAp%2BCf7YPrsR25hogXZ74ieO%2BG9d2Gm9KRnlv2%2FKQ8N5KE1s7VbJ0JkF6A5Awgk4yvjIZ1z7KJ456vMdMTL4QGnzHE7yPu6qbdGlBk4hH1LPplRF0UppEShjPFvnNKeMm4BkcW45UQ5VN3XVUyB6pKMsU%2FyII%2BDy67nUmzCfU0--ZUP4DvQj8FjYiDZQ--ibLRXR5iW6p4S%2FPN88iX6g%3D%3D';

  const query = `
    query MeCookies {
      meCookies {
        id
        email
        username
        firstName
        lastName
        role
        permissions
        createdAt
        updatedAt
      }
    }
  `;

  try {
    console.log('🔍 Testing GraphQL authentication...');
    console.log('📍 URL:', graphqlUrl);
    console.log('🍪 Cookie length:', cookieValue.length);
    
    const response = await fetch(graphqlUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': cookieValue
      },
      body: JSON.stringify({
        query: query
      })
    });

    console.log('📊 Response status:', response.status);
    console.log('📋 Response headers:', Object.fromEntries(response.headers.entries()));

    const result = await response.json();
    console.log('📄 Response body:', JSON.stringify(result, null, 2));

    if (result.errors) {
      console.log('❌ GraphQL Errors found');
      result.errors.forEach((error, index) => {
        console.log(`Error ${index + 1}:`, error.message);
        console.log('Path:', error.path);
        console.log('Code:', error.code);
      });
    }

    if (result.data && result.data.meCookies) {
      console.log('✅ Authentication successful!');
      console.log('👤 User:', result.data.meCookies.email);
    }

  } catch (error) {
    console.error('❌ Request failed:', error.message);
  }
}

// Also test with curl command
function generateCurlCommand() {
  const cookieValue = 'access_token=mq8i7AFWdKXnt3JgeLuuUzNtbe0vhDHBiBBTzuVJSaqFhW7javeOryA%2FaY7Oz6q5fAZW3OT6BN9L8Zc%2BfE3TkM8KY3E%2BavkeBovvVQsk4RfUnoNahrhk4DH97d3%2BBZQ3CCE08JGBYYDmVJJDTuBwOhwCB3q95Zx%2BIOtL8%2F6JheteiPjL5m3UGsZ7%2Bp1wHU64DJq%2F3walyd5tTvteDJcMBGKs2h1CNkNvOLJQHjGz7fJFDELepIzGoCdVoOt9Yzeq8z6%2FznW%2BulgJtCq%2F9yD7QoOY%2FLawtk8QlqtwAoKolSSmnnWgspwQTbeDUByMY5xaJxeI9gS3Us63S7OkM1DuYAHwJgYX4AOzldrxHYbInkfv2U5bqC%2BW%2B6dnzKrHdS%2Bc4WG5d%2BN5D7HPIKd%2BAYhqOkmQoC9X2p5keQogK5WtJcvlZ%2B4kLGJYsCoWacTGvWjVfZNcE0Y0xQRA3AVf7DhSRGy9MjjQlwK9Dg0XCFvPUfaAH2dbUs0PkFDO8WlKQytXlx4lrnewR5hCxFfDy028ic85Cf7jUJGoU%2FMjjRz6HLw%2BjhJhHtsyS2ERmCVj3wjmld8gLovF4O%2FAaEak1ZZGRfMCXK8eF0xeQ5yekC4h1mmgUVuXWpMAWsW9aC%2BF%2FUOZJhpx4urS5cxJr%2BYLvJt8oGaewgmXQ1ppaaDjPfS0wzWzy6RbGgwBB6u7DCGnTRpsithdWgeyQIWISY9cfsVO3rCBM3vM3F%2F8e18A7bgbq%2BS07sIBnUy3MW1%2FoxhUic%2BgH5Y9F6CxsFXUkYkzLuObDuxjfWMWpHWfJqb%2BVQaAp%2BCf7YPrsR25hogXZ74ieO%2BG9d2Gm9KRnlv2%2FKQ8N5KE1s7VbJ0JkF6A5Awgk4yvjIZ1z7KJ456vMdMTL4QGnzHE7yPu6qbdGlBk4hH1LPplRF0UppEShjPFvnNKeMm4BkcW45UQ5VN3XVUyB6pKMsU%2FyII%2BDy67nUmzCfU0--ZUP4DvQj8FjYiDZQ--ibLRXR5iW6p4S%2FPN88iX6g%3D%3D';
  
  const curlCommand = `curl -X POST http://localhost:3060/graphql \\
  -H "Content-Type: application/json" \\
  -H "Cookie: ${cookieValue}" \\
  -d '{"query":"query MeCookies { meCookies { id email username firstName lastName role permissions createdAt updatedAt } }"}'`;
  
  console.log('\n🔧 Equivalent curl command:');
  console.log(curlCommand);
}

testGraphQLAuth();
generateCurlCommand();
